"use client"
import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import arrowblog from "@/public/new assests/news icons/heroicons/homeimage/arrowcircleblog.svg"
import Image from "next/image";
import { getBlogsByViews, formatBlogDate, BlogData } from '@/api/blogs/blogs_api'

export default function HomePageBlog() {
  const router = useRouter()
  const [blogs, setBlogs] = useState<BlogData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchBlogs()
  }, [])

  const fetchBlogs = async () => {
    try {
      setLoading(true)
      const response = await getBlogsByViews(2) // Get top 2 blogs by views
      if (response.status) {
        setBlogs(response.data.data)
      }
    } catch (error) {
      console.error('Error fetching blogs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    router.push(`/blog/${blogId}/${slug}`)
  }

  const handleExploreMore = () => {
    router.push('/blog')
  }

  return (
    <div className="text-white  w-full flex flex-col justify-center items-center">
      <div className="w-[80%] px-10 flex flex-col gap-y-10 mt-10">
        {/* Header Section */}
        <div className="flex justify-between items-center ">
          <div>
            <h2 className="text-[78px] md:text-[78px] lg:text-[78px] font-bold font-satoshi leading-tight">
              <span className="p-4 bg-[#2B2B2B] mb-8 rounded-tl-xl rounded-tr-xl rounded-br-xl">Latest Insights</span>
              <br />
              <span className="px-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl rounded-bl-xl">& News</span>
            </h2>
          </div>
          <div>
            <button
              onClick={handleExploreMore}
              className="bg-transparent border border-white text-white px-6 py-3 rounded-full text-[24px] font-medium hover:bg-white hover:text-black transition-all duration-300"
            >
              Explore More
            </button>
          </div>
        </div>

        {/* Blog Grid */}
        {loading ? (
          <div className="text-center py-20">
            <div className="text-white text-xl">Loading blogs...</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px] md:gap-[38px] lg:gap-[48px]">
            {blogs.map((blog) => (
              <div
                key={blog._id}
                onClick={() => handleBlogClick(blog._id, blog.title)}
                className="bg-[#2B2B2B] hover:bg-[#363636] rounded-[30px] cursor-pointer p-6 flex flex-col justify-between relative overflow-hidden transition-all duration-300"
              >
                {/* Blog Image */}
                <div className="relative h-[381px] md:h-[381px] lg:h-[381px] rounded-[30px] overflow-hidden mb-6">
                  <Image
                    src={blog.imageUrl}
                    alt={blog.title}
                    width={500}
                    height={381}
                    className="object-cover w-full h-full"
                  />
                  {/* Arrow Icon - Bottom Right */}
                  <div className="absolute bottom-4 right-4">
                    <Image
                      src={arrowblog}
                      alt="Arrow"
                      className="w-[61.45px] h-[60.69px]"
                    />
                  </div>
                </div>

                {/* Blog Content */}
                <div className="flex flex-col justify-between gap-y-5">
                   {/* Date and Views Info */}
                  <div className="flex flex-row justify-between items-center ">
                    <div className="flex flex-row justify-center items-center gap-3">
                        <p className="text-white/60 text-[18px]">{formatBlogDate(blog.createdAt)}</p>
                        <p className="text-white/60 text-[18px]">•</p>
                        <p className="text-white/60 text-[18px]">{blog.views} views</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-[32px] md:text-[32px] lg:text-[32px] font-bold text-white leading-tight mb-3">
                      {blog.title}
                    </h3>
                  </div>

                  {/* Categories */}
                  <div className="flex flex-wrap gap-2">
                    {blog.category.slice(0, 2).map((cat, index) => (
                      <span key={index} className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm">
                        {cat}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}